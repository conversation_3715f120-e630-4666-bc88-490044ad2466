/*
 * Copyright 2025 NXP
 * NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"
#include "user_api.h"

void setup_scr_lcd_run(lv_ui *ui)
{
    // Write codes lcd_run
    ui->lcd_run = lv_obj_create(NULL);
    lv_obj_set_size(ui->lcd_run, 960, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_run, LV_SCROLLBAR_MODE_OFF);

    // Write style for lcd_run, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->lcd_run, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run, lv_color_hex(0x282929), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run, LV_GRAD_DIR_NONE, LV_PART_MAIN | LV_STATE_DEFAULT);

    // Write codes lcd_run_run_animimg
    ui->lcd_run_run_animimg = lv_animimg_create(ui->lcd_run);
    lv_animimg_set_src(ui->lcd_run_run_animimg, (const void **)lcd_run_run_animimg_imgs, 12);
    lv_animimg_set_duration(ui->lcd_run_run_animimg, 50 * 12);
    lv_animimg_set_repeat_count(ui->lcd_run_run_animimg, 1);
    
    // 对动画图片进行缩放，动画图片基于lv_img组件，可以直接设置缩放属性
    // 通过以下方式对动画图片设置缩放比例，384代表1.5倍大小（256为原始大小）
    lv_img_set_zoom(ui->lcd_run_run_animimg, 128);
    
    lv_animimg_start(ui->lcd_run_run_animimg);
    lv_obj_set_pos(ui->lcd_run_run_animimg, 0, 0);
    lv_obj_set_size(ui->lcd_run_run_animimg, 528, 360);


    // Write codes lcd_run_rinsetemp_cont
    ui->lcd_run_rinsetemp_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_cont, 680, 80);
    lv_obj_set_size(ui->lcd_run_rinsetemp_cont, 250, 120);
    lv_obj_set_scrollbar_mode(ui->lcd_run_rinsetemp_cont, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_run_rinsetemp_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinsetemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_cont, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinsetemp_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_rinsetemp_cont, lv_color_hex(0x383838), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_rinsetemp_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinsetemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinsetemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinsetemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinsetemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinsetemp_cont, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(ui->lcd_run_rinsetemp_cont, lv_color_hex(0xebff00), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui->lcd_run_rinsetemp_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui->lcd_run_rinsetemp_cont, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(ui->lcd_run_rinsetemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(ui->lcd_run_rinsetemp_cont, 5, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_rinsetemp_img_2
    ui->lcd_run_rinsetemp_img_2 = lv_img_create(ui->lcd_run_rinsetemp_cont);
    lv_obj_add_flag(ui->lcd_run_rinsetemp_img_2, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_rinsetemp_img_2, LVGL_PATH(11.png));
    lv_img_set_pivot(ui->lcd_run_rinsetemp_img_2, 50,50);
    lv_img_set_angle(ui->lcd_run_rinsetemp_img_2, 0);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_img_2, 218, 11);
    lv_obj_set_size(ui->lcd_run_rinsetemp_img_2, 20, 20);

    //Write style for lcd_run_rinsetemp_img_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_rinsetemp_img_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_recolor(ui->lcd_run_rinsetemp_img_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_rinsetemp_img_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_img_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_rinsetemp_img_2, true, LV_PART_MAIN|LV_STATE_DEFAULT);
// 此处ok
    //Write codes lcd_run_rinsetemp_label_5
    ui->lcd_run_rinsetemp_label_5 = lv_label_create(ui->lcd_run_rinsetemp_cont);
    lv_label_set_text(ui->lcd_run_rinsetemp_label_5, "洗涤温度\n");
    lv_label_set_long_mode(ui->lcd_run_rinsetemp_label_5, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_label_5, 38, 11);
    lv_obj_set_size(ui->lcd_run_rinsetemp_label_5, 72, 16);

    //Write style for lcd_run_rinsetemp_label_5, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinsetemp_label_5, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinsetemp_label_5, &lv_font_FY_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinsetemp_label_5, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinsetemp_label_5, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinsetemp_label_5, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinsetemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_rinsetemp_img_1
    ui->lcd_run_rinsetemp_img_1 = lv_img_create(ui->lcd_run_rinsetemp_cont);
    lv_obj_add_flag(ui->lcd_run_rinsetemp_img_1, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_rinsetemp_img_1, LVGL_PATH(10.png));
    lv_img_set_pivot(ui->lcd_run_rinsetemp_img_1, 50,50);
    lv_img_set_angle(ui->lcd_run_rinsetemp_img_1, 0);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_img_1, 13, 11);
    lv_obj_set_size(ui->lcd_run_rinsetemp_img_1, 20, 40);

    //Write style for lcd_run_rinsetemp_img_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_rinsetemp_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_rinsetemp_img_1, 128, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_rinsetemp_img_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_rinsetemp_label_4
    ui->lcd_run_rinsetemp_label_4 = lv_label_create(ui->lcd_run_rinsetemp_cont);
    lv_label_set_text(ui->lcd_run_rinsetemp_label_4, "60");
    lv_label_set_long_mode(ui->lcd_run_rinsetemp_label_4, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_label_4, 91, 24);
    lv_obj_set_size(ui->lcd_run_rinsetemp_label_4, 150, 75);

    //Write style for lcd_run_rinsetemp_label_4, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinsetemp_label_4, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinsetemp_label_4, &lv_font_HT_75, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinsetemp_label_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinsetemp_label_4, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinsetemp_label_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinsetemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_rinsetemp_labe_3
    ui->lcd_run_rinsetemp_labe_3 = lv_label_create(ui->lcd_run_rinsetemp_cont);
    lv_label_set_text(ui->lcd_run_rinsetemp_labe_3, "80");
    lv_label_set_long_mode(ui->lcd_run_rinsetemp_labe_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_labe_3, 16, 53);
    lv_obj_set_size(ui->lcd_run_rinsetemp_labe_3, 70, 35);

    //Write style for lcd_run_rinsetemp_labe_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinsetemp_labe_3, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinsetemp_labe_3, &lv_font_HT_35, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinsetemp_labe_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinsetemp_labe_3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinsetemp_labe_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinsetemp_labe_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_rinsetemp_label_2
    ui->lcd_run_rinsetemp_label_2 = lv_label_create(ui->lcd_run_rinsetemp_cont);
    lv_label_set_text(ui->lcd_run_rinsetemp_label_2, "设定");
    lv_label_set_long_mode(ui->lcd_run_rinsetemp_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_label_2, 58, 99);
    lv_obj_set_size(ui->lcd_run_rinsetemp_label_2, 32, 14);

    //Write style for lcd_run_rinsetemp_label_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinsetemp_label_2, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinsetemp_label_2, &lv_font_FY_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinsetemp_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinsetemp_label_2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinsetemp_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinsetemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//此处ok
    // //Write codes lcd_run_rinsetemp_line
    ui->lcd_run_rinsetemp_line = lv_line_create(ui->lcd_run_rinsetemp_cont);
    static lv_point_t lcd_run_rinsetemp_line[] = {{0, 0},{0, 70},};
    lv_line_set_points(ui->lcd_run_rinsetemp_line, lcd_run_rinsetemp_line, 2);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_line, 101, 34);
    lv_obj_set_size(ui->lcd_run_rinsetemp_line, 2, 70);

    //Write style for lcd_run_rinsetemp_line, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_run_rinsetemp_line, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_run_rinsetemp_line, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_run_rinsetemp_line, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_run_rinsetemp_line, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_rinsetemp_label_1
    ui->lcd_run_rinsetemp_label_1 = lv_label_create(ui->lcd_run_rinsetemp_cont);
    lv_label_set_text(ui->lcd_run_rinsetemp_label_1, "实际\n");
    lv_label_set_long_mode(ui->lcd_run_rinsetemp_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinsetemp_label_1, 204, 99);
    lv_obj_set_size(ui->lcd_run_rinsetemp_label_1, 32, 14);

    //Write style for lcd_run_rinsetemp_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinsetemp_label_1, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinsetemp_label_1, &lv_font_FY_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinsetemp_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinsetemp_label_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinsetemp_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinsetemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_washtemp_cont
    ui->lcd_run_washtemp_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_washtemp_cont, 680, 220);
    lv_obj_set_size(ui->lcd_run_washtemp_cont, 250, 120);
    lv_obj_set_scrollbar_mode(ui->lcd_run_washtemp_cont, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_run_washtemp_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
     lv_obj_set_style_border_width(ui->lcd_run_washtemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_radius(ui->lcd_run_washtemp_cont, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_bg_opa(ui->lcd_run_washtemp_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_bg_color(ui->lcd_run_washtemp_cont, lv_color_hex(0x383838), LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_bg_grad_dir(ui->lcd_run_washtemp_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_pad_top(ui->lcd_run_washtemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_pad_bottom(ui->lcd_run_washtemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_pad_left(ui->lcd_run_washtemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_pad_right(ui->lcd_run_washtemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
     lv_obj_set_style_shadow_width(ui->lcd_run_washtemp_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_washtemp_img_2
    ui->lcd_run_washtemp_img_2 = lv_img_create(ui->lcd_run_washtemp_cont);
    lv_obj_add_flag(ui->lcd_run_washtemp_img_2, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_washtemp_img_2, LVGL_PATH(10.png));
    lv_img_set_pivot(ui->lcd_run_washtemp_img_2, 50,50);
    lv_img_set_angle(ui->lcd_run_washtemp_img_2, 0);
    lv_obj_set_pos(ui->lcd_run_washtemp_img_2, 13, 11);
    lv_obj_set_size(ui->lcd_run_washtemp_img_2, 20, 40);

    //Write style for lcd_run_washtemp_img_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_washtemp_img_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_washtemp_img_2, 128, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_washtemp_img_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_washtemp_img_2, true, LV_PART_MAIN|LV_STATE_DEFAULT);
//此处ok
    // //Write codes lcd_run_washtemp_label_5
    ui->lcd_run_washtemp_label_5 = lv_label_create(ui->lcd_run_washtemp_cont);
    lv_label_set_text(ui->lcd_run_washtemp_label_5, "漂洗温度\n");
    lv_label_set_long_mode(ui->lcd_run_washtemp_label_5, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_washtemp_label_5, 38, 11);
    lv_obj_set_size(ui->lcd_run_washtemp_label_5, 72, 16);

    //Write style for lcd_run_washtemp_label_5, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_washtemp_label_5, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_washtemp_label_5, &lv_font_FY_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_washtemp_label_5, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_washtemp_label_5, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_washtemp_label_5, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_washtemp_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_washtemp_label_4
    ui->lcd_run_washtemp_label_4 = lv_label_create(ui->lcd_run_washtemp_cont);
    lv_label_set_text(ui->lcd_run_washtemp_label_4, "80");
    lv_label_set_long_mode(ui->lcd_run_washtemp_label_4, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_washtemp_label_4, 19, 57);
    lv_obj_set_size(ui->lcd_run_washtemp_label_4, 70, 35);

    //Write style for lcd_run_washtemp_label_4, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_washtemp_label_4, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_washtemp_label_4, &lv_font_HT_35, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_washtemp_label_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_washtemp_label_4, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_washtemp_label_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_washtemp_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_washtemp_label_3
    ui->lcd_run_washtemp_label_3 = lv_label_create(ui->lcd_run_washtemp_cont);
    lv_label_set_text(ui->lcd_run_washtemp_label_3, "设定");
    lv_label_set_long_mode(ui->lcd_run_washtemp_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_washtemp_label_3, 58, 98);
    lv_obj_set_size(ui->lcd_run_washtemp_label_3, 32, 14);

    //Write style for lcd_run_washtemp_label_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_washtemp_label_3, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_washtemp_label_3, &lv_font_FY_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_washtemp_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_washtemp_label_3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_washtemp_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_washtemp_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_washtemp_label_2
    ui->lcd_run_washtemp_label_2 = lv_label_create(ui->lcd_run_washtemp_cont);
    lv_label_set_text(ui->lcd_run_washtemp_label_2, "60");
    lv_label_set_long_mode(ui->lcd_run_washtemp_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_washtemp_label_2, 92, 23);
    lv_obj_set_size(ui->lcd_run_washtemp_label_2, 150, 75);

    //Write style for lcd_run_washtemp_label_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_washtemp_label_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_washtemp_label_2, &lv_font_HT_75, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_washtemp_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_washtemp_label_2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_washtemp_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_washtemp_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write codes lcd_run_washtemp_line
    ui->lcd_run_washtemp_line = lv_line_create(ui->lcd_run_washtemp_cont);
    static lv_point_t lcd_run_washtemp_line[] = {{0, 0},{0, 70},};
    lv_line_set_points(ui->lcd_run_washtemp_line, lcd_run_washtemp_line, 2);
    lv_obj_set_pos(ui->lcd_run_washtemp_line, 105, 36);
    lv_obj_set_size(ui->lcd_run_washtemp_line, 2, 70);

    //Write style for lcd_run_washtemp_line, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_run_washtemp_line, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_run_washtemp_line, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_run_washtemp_line, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_run_washtemp_line, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write codes lcd_run_washtemp_label_1
    ui->lcd_run_washtemp_label_1 = lv_label_create(ui->lcd_run_washtemp_cont);
    lv_label_set_text(ui->lcd_run_washtemp_label_1, "实际\n");
    lv_label_set_long_mode(ui->lcd_run_washtemp_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_washtemp_label_1, 204, 99);
    lv_obj_set_size(ui->lcd_run_washtemp_label_1, 32, 14);

   // Write style for lcd_run_washtemp_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_washtemp_label_1, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_washtemp_label_1, &lv_font_FY_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_washtemp_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_washtemp_label_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_washtemp_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_washtemp_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write codes lcd_run_washtemp_img_1
    ui->lcd_run_washtemp_img_1 = lv_img_create(ui->lcd_run_washtemp_cont);
    lv_obj_add_flag(ui->lcd_run_washtemp_img_1, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_washtemp_img_1, LVGL_PATH(11.png));
    lv_img_set_pivot(ui->lcd_run_washtemp_img_1, 50,50);
    lv_img_set_angle(ui->lcd_run_washtemp_img_1, 0);
    lv_obj_set_pos(ui->lcd_run_washtemp_img_1, 218, 11);
    lv_obj_set_size(ui->lcd_run_washtemp_img_1, 20, 20);

   // Write style for lcd_run_washtemp_img_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_washtemp_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_washtemp_img_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_washtemp_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_washtemp_img_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write codes lcd_run_workmode_cont
    ui->lcd_run_workmode_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_workmode_cont, 300, -40);
    lv_obj_set_size(ui->lcd_run_workmode_cont, 630, 40);
    lv_obj_set_scrollbar_mode(ui->lcd_run_workmode_cont, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_run_workmode_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_workmode_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_workmode_cont, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_workmode_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_workmode_cont, lv_color_hex(0x383838), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_workmode_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_workmode_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_workmode_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_workmode_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_workmode_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_workmode_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write codes lcd_run_workmode_label_1
    ui->lcd_run_workmode_label_1 = lv_label_create(ui->lcd_run_workmode_cont);
    lv_label_set_text(ui->lcd_run_workmode_label_1, "节 能");
    lv_label_set_long_mode(ui->lcd_run_workmode_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_workmode_label_1, 0, 0);
    lv_obj_set_size(ui->lcd_run_workmode_label_1, 210, 40);

    //Write style for lcd_run_workmode_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_workmode_label_1, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_workmode_label_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_workmode_label_1, &lv_font_HT_34, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_workmode_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_workmode_label_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_workmode_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_workmode_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_workmode_label_1, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_workmode_label_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_workmode_label_1, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write style for lcd_run_workmode_label_1, Part: LV_PART_MAIN, State: LV_STATE_DISABLED.
    lv_obj_set_style_border_width(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_radius(ui->lcd_run_workmode_label_1, 5, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_color(ui->lcd_run_workmode_label_1, lv_color_hex(0x797979), LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_font(ui->lcd_run_workmode_label_1, &lv_font_HT_26, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_opa(ui->lcd_run_workmode_label_1, 255, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_letter_space(ui->lcd_run_workmode_label_1, 2, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_line_space(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_align(ui->lcd_run_workmode_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_top(ui->lcd_run_workmode_label_1, 7, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_right(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_bottom(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_left(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_shadow_width(ui->lcd_run_workmode_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);

    // Write codes lcd_run_workmode_label_2
    ui->lcd_run_workmode_label_2 = lv_label_create(ui->lcd_run_workmode_cont);
    lv_label_set_text(ui->lcd_run_workmode_label_2, "标 准");
    lv_label_set_long_mode(ui->lcd_run_workmode_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_workmode_label_2, 210, 0);
    lv_obj_set_size(ui->lcd_run_workmode_label_2, 210, 40);

    // Write style for lcd_run_workmode_label_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_workmode_label_2, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_workmode_label_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_workmode_label_2, &lv_font_HT_34, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_workmode_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_workmode_label_2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_workmode_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_workmode_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_workmode_label_2, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_workmode_label_2, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_workmode_label_2, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write style for lcd_run_workmode_label_2, Part: LV_PART_MAIN, State: LV_STATE_DISABLED.
    lv_obj_set_style_border_width(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_radius(ui->lcd_run_workmode_label_2, 5, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_color(ui->lcd_run_workmode_label_2, lv_color_hex(0x797979), LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_font(ui->lcd_run_workmode_label_2, &lv_font_HT_26, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_opa(ui->lcd_run_workmode_label_2, 255, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_letter_space(ui->lcd_run_workmode_label_2, 2, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_line_space(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_align(ui->lcd_run_workmode_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_top(ui->lcd_run_workmode_label_2, 7, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_right(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_bottom(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_left(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_shadow_width(ui->lcd_run_workmode_label_2, 0, LV_PART_MAIN|LV_STATE_DISABLED);

    // Write codes lcd_run_workmode_label_3
    ui->lcd_run_workmode_label_3 = lv_label_create(ui->lcd_run_workmode_cont);
    lv_label_set_text(ui->lcd_run_workmode_label_3, "强 力");
    lv_label_set_long_mode(ui->lcd_run_workmode_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_workmode_label_3, 420, 0);
    lv_obj_set_size(ui->lcd_run_workmode_label_3, 210, 40);

    // //Write style for lcd_run_workmode_label_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_workmode_label_3, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_workmode_label_3, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_workmode_label_3, &lv_font_HT_34, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_workmode_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_workmode_label_3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_workmode_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_workmode_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_workmode_label_3, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_workmode_label_3, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_workmode_label_3, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write style for lcd_run_workmode_label_3, Part: LV_PART_MAIN, State: LV_STATE_DISABLED.
    lv_obj_set_style_border_width(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_radius(ui->lcd_run_workmode_label_3, 5, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_color(ui->lcd_run_workmode_label_3, lv_color_hex(0x797979), LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_font(ui->lcd_run_workmode_label_3, &lv_font_HT_26, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_opa(ui->lcd_run_workmode_label_3, 255, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_letter_space(ui->lcd_run_workmode_label_3, 2, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_line_space(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_align(ui->lcd_run_workmode_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_top(ui->lcd_run_workmode_label_3, 7, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_right(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_bottom(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_left(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_shadow_width(ui->lcd_run_workmode_label_3, 0, LV_PART_MAIN|LV_STATE_DISABLED);

    // Write codes lcd_run_wash_cont
    ui->lcd_run_wash_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_wash_cont, 300, 80);
    lv_obj_set_size(ui->lcd_run_wash_cont, 170, 60);
    lv_obj_set_scrollbar_mode(ui->lcd_run_wash_cont, LV_SCROLLBAR_MODE_OFF);

    // Write style for lcd_run_wash_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_wash_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_wash_cont, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_wash_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_wash_cont, lv_color_hex(0x383838), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_wash_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_wash_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_wash_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_wash_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_wash_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_wash_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write codes lcd_run_wash_img
    ui->lcd_run_wash_img = lv_img_create(ui->lcd_run_wash_cont);
    lv_obj_add_flag(ui->lcd_run_wash_img, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_wash_img, LVGL_PATH(12.png));
    lv_img_set_pivot(ui->lcd_run_wash_img, 50,50);
    lv_img_set_angle(ui->lcd_run_wash_img, 0);
    lv_obj_set_pos(ui->lcd_run_wash_img, 6, 6);
    lv_obj_set_size(ui->lcd_run_wash_img, 20, 20);

    // Write style for lcd_run_wash_img, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_wash_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_wash_img, 129, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_wash_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_wash_img, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    // Write codes lcd_run_wash_line
    ui->lcd_run_wash_line = lv_line_create(ui->lcd_run_wash_cont);
    static lv_point_t lcd_run_wash_line[] = {{0, 0},{0, 40},};
    lv_line_set_points(ui->lcd_run_wash_line, lcd_run_wash_line, 2);
    lv_obj_set_pos(ui->lcd_run_wash_line, 85, 11);
    lv_obj_set_size(ui->lcd_run_wash_line, 2, 40);

     //Write style for lcd_run_wash_line, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_run_wash_line, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_run_wash_line, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_run_wash_line, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_run_wash_line, true, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_wash_label3
    ui->lcd_run_wash_label3 = lv_label_create(ui->lcd_run_wash_cont);
    lv_label_set_text(ui->lcd_run_wash_label3, "洗涤");
    lv_label_set_long_mode(ui->lcd_run_wash_label3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_wash_label3, 27, 31);
    lv_obj_set_size(ui->lcd_run_wash_label3, 50, 22);

    // //Write style for lcd_run_wash_label3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_wash_label3, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_wash_label3, &lv_font_FY_22, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_wash_label3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_wash_label3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_wash_label3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_wash_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_wash_label2
    ui->lcd_run_wash_label2 = lv_label_create(ui->lcd_run_wash_cont);
    lv_label_set_text(ui->lcd_run_wash_label2, "水位");
    lv_label_set_long_mode(ui->lcd_run_wash_label2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_wash_label2, 27, 6);
    lv_obj_set_size(ui->lcd_run_wash_label2, 40, 14);

     //Write style for lcd_run_wash_label2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_wash_label2, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_wash_label2, &lv_font_FY_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_wash_label2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_wash_label2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_wash_label2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_wash_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_run_wash_label
    ui->lcd_run_wash_label = lv_label_create(ui->lcd_run_wash_cont);
    lv_label_set_text(ui->lcd_run_wash_label, "正常\n");
    lv_label_set_long_mode(ui->lcd_run_wash_label, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_wash_label, 89, 15);
    lv_obj_set_size(ui->lcd_run_wash_label, 71, 28);

     //Write style for lcd_run_wash_label, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_wash_label, lv_color_hex(0xdfff7a), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_wash_label, &lv_font_HT_28, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_wash_label, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_wash_label, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_wash_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_wash_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//此处ok
     //Write codes lcd_run_rinse_cont
    ui->lcd_run_rinse_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_rinse_cont, 488, 80);
    lv_obj_set_size(ui->lcd_run_rinse_cont, 170, 60);
    lv_obj_set_scrollbar_mode(ui->lcd_run_rinse_cont, LV_SCROLLBAR_MODE_OFF);

     //Write style for lcd_run_rinse_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinse_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinse_cont, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinse_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_rinse_cont, lv_color_hex(0x383838), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_rinse_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinse_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinse_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinse_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinse_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinse_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // //Write codes lcd_run_rinse_label
    ui->lcd_run_rinse_label = lv_label_create(ui->lcd_run_rinse_cont);
    lv_label_set_text(ui->lcd_run_rinse_label, "正常");
    lv_label_set_long_mode(ui->lcd_run_rinse_label, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinse_label, 92, 15);
    lv_obj_set_size(ui->lcd_run_rinse_label, 71, 28);

     //Write style for lcd_run_rinse_label, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinse_label, lv_color_hex(0xdfff7a), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinse_label, &lv_font_HT_28, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinse_label, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinse_label, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinse_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinse_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_rinse_label3
    ui->lcd_run_rinse_label3 = lv_label_create(ui->lcd_run_rinse_cont);
    lv_label_set_text(ui->lcd_run_rinse_label3, "漂洗");
    lv_label_set_long_mode(ui->lcd_run_rinse_label3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinse_label3, 27, 31);
    lv_obj_set_size(ui->lcd_run_rinse_label3, 50, 22);

     //Write style for lcd_run_rinse_label3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinse_label3, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinse_label3, &lv_font_FY_22, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinse_label3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinse_label3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinse_label3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinse_label3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_rinse_line
    ui->lcd_run_rinse_line = lv_line_create(ui->lcd_run_rinse_cont);
    static lv_point_t lcd_run_rinse_line[] = {{0, 0},{0, 40},};
    lv_line_set_points(ui->lcd_run_rinse_line, lcd_run_rinse_line, 2);
    lv_obj_set_pos(ui->lcd_run_rinse_line, 85, 10);
    lv_obj_set_size(ui->lcd_run_rinse_line, 2, 40);

    //Write style for lcd_run_rinse_line, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_run_rinse_line, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_run_rinse_line, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_run_rinse_line, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_run_rinse_line, true, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_rinse_label2
    ui->lcd_run_rinse_label2 = lv_label_create(ui->lcd_run_rinse_cont);
    lv_label_set_text(ui->lcd_run_rinse_label2, "水位");
    lv_label_set_long_mode(ui->lcd_run_rinse_label2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_rinse_label2, 27, 6);
    lv_obj_set_size(ui->lcd_run_rinse_label2, 40, 14);

    //Write style for lcd_run_rinse_label2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_rinse_label2, lv_color_hex(0xababab), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_rinse_label2, &lv_font_FY_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_rinse_label2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_rinse_label2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_rinse_label2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_rinse_label2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_run_rinse_img
    ui->lcd_run_rinse_img = lv_img_create(ui->lcd_run_rinse_cont);
    lv_obj_add_flag(ui->lcd_run_rinse_img, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_rinse_img, LVGL_PATH(12.png));
    lv_img_set_pivot(ui->lcd_run_rinse_img, 50,50);
    lv_img_set_angle(ui->lcd_run_rinse_img, 0);
    lv_obj_set_pos(ui->lcd_run_rinse_img, 6, 6);
    lv_obj_set_size(ui->lcd_run_rinse_img, 20, 20);
//此处正常
    //Write style for lcd_run_rinse_img, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_rinse_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_rinse_img, 129, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_rinse_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_rinse_img, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_run_label_3
    ui->lcd_run_label_3 = lv_label_create(ui->lcd_run_rinse_cont);
    lv_label_set_text(ui->lcd_run_label_3, "正常");
    lv_label_set_long_mode(ui->lcd_run_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_label_3, -590, -71);
    lv_obj_set_size(ui->lcd_run_label_3, 71, 28);

    //Write style for lcd_run_label_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_label_3, lv_color_hex(0xdfff7a), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_label_3, &lv_font_HT_28, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_label_3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_label_4
    ui->lcd_run_label_4 = lv_label_create(ui->lcd_run_rinse_cont);
    lv_label_set_text(ui->lcd_run_label_4, "水满");
    lv_label_set_long_mode(ui->lcd_run_label_4, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_label_4, -595, 23);
    lv_obj_set_size(ui->lcd_run_label_4, 71, 28);

    //Write style for lcd_run_label_4, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_label_4, lv_color_hex(0xffbc54), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_label_4, &lv_font_HT_28, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_label_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_label_4, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_label_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_door_cont
    ui->lcd_run_door_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_door_cont, 300, 290);
    lv_obj_set_size(ui->lcd_run_door_cont, 360, 50);
    lv_obj_set_scrollbar_mode(ui->lcd_run_door_cont, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_run_door_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_door_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_door_cont, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_door_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_door_cont, lv_color_hex(0x383838), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_door_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_door_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_door_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_door_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_door_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_door_cont, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(ui->lcd_run_door_cont, lv_color_hex(0x000000), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui->lcd_run_door_cont, 167, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui->lcd_run_door_cont, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(ui->lcd_run_door_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(ui->lcd_run_door_cont, 5, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_door_img
    ui->lcd_run_door_img = lv_img_create(ui->lcd_run_door_cont);
    lv_obj_add_flag(ui->lcd_run_door_img, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_door_img, LVGL_PATH(13.png));
    lv_img_set_pivot(ui->lcd_run_door_img, 50,50);
    lv_img_set_angle(ui->lcd_run_door_img, 0);
    lv_obj_set_pos(ui->lcd_run_door_img, 21, 12);
    lv_obj_set_size(ui->lcd_run_door_img, 20, 26);

    //Write style for lcd_run_door_img, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_door_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_door_img, 129, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_door_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_door_img, true, LV_PART_MAIN|LV_STATE_DEFAULT);
//此处正常
    //Write codes lcd_run_door_label
    ui->lcd_run_door_label = lv_label_create(ui->lcd_run_door_cont);
    lv_label_set_text(ui->lcd_run_door_label, "机门打开");
    lv_label_set_long_mode(ui->lcd_run_door_label, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_door_label, 135, 11);
    lv_obj_set_size(ui->lcd_run_door_label, 127, 25);

    //Write style for lcd_run_door_label, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_door_label, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_door_label, &lv_font_FY_25, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_door_label, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_door_label, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_door_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_door_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_door_line_1
    ui->lcd_run_door_line_1 = lv_line_create(ui->lcd_run_door_cont);
    static lv_point_t lcd_run_door_line_1[] = {{0, 0},{60, 0},};
    lv_line_set_points(ui->lcd_run_door_line_1, lcd_run_door_line_1, 2);
    lv_obj_set_pos(ui->lcd_run_door_line_1, 63, 29);
    lv_obj_set_size(ui->lcd_run_door_line_1, 60, 2);

    //Write style for lcd_run_door_line_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_run_door_line_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_run_door_line_1, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_run_door_line_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_run_door_line_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_run_door_line_2
    ui->lcd_run_door_line_2 = lv_line_create(ui->lcd_run_door_cont);
    static lv_point_t lcd_run_door_line_2[] = {{0, 0},{60, 0},};
    lv_line_set_points(ui->lcd_run_door_line_2, lcd_run_door_line_2, 2);
    lv_obj_set_pos(ui->lcd_run_door_line_2, 273, 29);
    lv_obj_set_size(ui->lcd_run_door_line_2, 60, 2);

    //Write style for lcd_run_door_line_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_run_door_line_2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_run_door_line_2, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_run_door_line_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_run_door_line_2, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_run_label_2
    ui->lcd_run_label_2 = lv_label_create(ui->lcd_run_door_cont);
    lv_label_set_text(ui->lcd_run_label_2, "机门关闭");
    lv_label_set_long_mode(ui->lcd_run_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_label_2, -440, -147);
    lv_obj_set_size(ui->lcd_run_label_2, 127, 25);

    //Write style for lcd_run_label_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_label_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_label_2, &lv_font_FY_25, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_label_2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_wait_cont
    ui->lcd_run_wait_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_wait_cont, 300, 160);
    lv_obj_set_size(ui->lcd_run_wait_cont, 360, 110);
    lv_obj_set_scrollbar_mode(ui->lcd_run_wait_cont, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_run_wait_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_wait_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_wait_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_wait_cont, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_wait_cont, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_wait_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_wait_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_wait_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_wait_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_wait_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_wait_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//此处正常
    //Write codes lcd_run_wait_canvas
    ui->lcd_run_wait_canvas = lv_canvas_create(ui->lcd_run_wait_cont);
    static lv_color_t buf_lcd_run_wait_canvas[LV_CANVAS_BUF_SIZE_TRUE_COLOR_ALPHA(300, 45)];
    lv_canvas_set_buffer(ui->lcd_run_wait_canvas, buf_lcd_run_wait_canvas, 300, 45, LV_IMG_CF_TRUE_COLOR_ALPHA);
    lv_canvas_fill_bg(ui->lcd_run_wait_canvas, lv_color_hex(0xffffff), 0);
    //此处正常
    //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_0;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_0);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(14.png), &lcd_run_wait_canvas_img_dsc_0);

    //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_1;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_1);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(15.png), &lcd_run_wait_canvas_img_dsc_1);

      //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_2;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_2);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(16.png), &lcd_run_wait_canvas_img_dsc_2);

     //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_3;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_3);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(17.png), &lcd_run_wait_canvas_img_dsc_3);

     //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_4;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_4);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(18.png), &lcd_run_wait_canvas_img_dsc_4);

     //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_5;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_5);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(19.png), &lcd_run_wait_canvas_img_dsc_5);

     //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_6;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_6);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(20.png), &lcd_run_wait_canvas_img_dsc_6);

     //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_7;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_7);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(21.png), &lcd_run_wait_canvas_img_dsc_7);

    //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_8;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_8);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(22.png), &lcd_run_wait_canvas_img_dsc_8);

    //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_9;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_9);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(23.png), &lcd_run_wait_canvas_img_dsc_9);

    //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_10;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_10);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(24.png), &lcd_run_wait_canvas_img_dsc_10);

    //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_11;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_11);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(25.png), &lcd_run_wait_canvas_img_dsc_11);

    //Canvas draw image
    lv_draw_img_dsc_t lcd_run_wait_canvas_img_dsc_12;
    lv_draw_img_dsc_init(&lcd_run_wait_canvas_img_dsc_12);
    lv_canvas_draw_img(ui->lcd_run_wait_canvas, 0, 0, LVGL_PATH(26.png), &lcd_run_wait_canvas_img_dsc_12);

    lv_obj_set_pos(ui->lcd_run_wait_canvas, 35, 57);
    lv_obj_set_size(ui->lcd_run_wait_canvas, 300, 45);
    lv_obj_set_scrollbar_mode(ui->lcd_run_wait_canvas, LV_SCROLLBAR_MODE_OFF);
//此处正常
    //Write codes lcd_run_wait_label
     ui->lcd_run_wait_label = lv_label_create(ui->lcd_run_wait_cont);
    lv_label_set_text(ui->lcd_run_wait_label, "等待...");
    lv_label_set_long_mode(ui->lcd_run_wait_label, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_wait_label, 35, 0);
    lv_obj_set_size(ui->lcd_run_wait_label, 278, 40);

    //Write style for lcd_run_wait_label, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_wait_label, lv_color_hex(0xecfd91), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_wait_label, &lv_font_HT_40, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_wait_label, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_wait_label, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_wait_label, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_wait_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//此处正常
     //Write codes lcd_run_warning_cont
     ui->lcd_run_warning_cont = lv_obj_create(ui->lcd_run);
    lv_obj_set_pos(ui->lcd_run_warning_cont, 0, 0);
    lv_obj_set_size(ui->lcd_run_warning_cont, 960, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_run_warning_cont, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_flag(ui->lcd_run_warning_cont, LV_OBJ_FLAG_HIDDEN);

    //Write style for lcd_run_warning_cont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_warning_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_warning_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_warning_cont, 130, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_run_warning_cont, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_run_warning_cont, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_warning_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_warning_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_warning_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_warning_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_warning_cont, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_run_warning_img
     ui->lcd_run_warning_img = lv_img_create(ui->lcd_run_warning_cont);
    lv_obj_add_flag(ui->lcd_run_warning_img, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->lcd_run_warning_img, LVGL_PATH(39.png));
    lv_img_set_pivot(ui->lcd_run_warning_img, 50,50);
    lv_img_set_angle(ui->lcd_run_warning_img, 0);
    lv_obj_set_pos(ui->lcd_run_warning_img, 179, 49);
    lv_obj_set_size(ui->lcd_run_warning_img, 600, 260);

    //Write style for lcd_run_warning_img, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->lcd_run_warning_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->lcd_run_warning_img, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_warning_img, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->lcd_run_warning_img, true, LV_PART_MAIN|LV_STATE_DEFAULT);

     //Write codes lcd_run_warning_label
     ui->lcd_run_warning_label = lv_label_create(ui->lcd_run_warning_cont);
    lv_label_set_text(ui->lcd_run_warning_label, " 探头故障报警");
    lv_label_set_long_mode(ui->lcd_run_warning_label, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_warning_label, 180, 220);
    lv_obj_set_size(ui->lcd_run_warning_label, 600, 30);

    //Write style for lcd_run_warning_label, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_warning_label, lv_color_hex(0x000000), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_warning_label, &lv_font_HT_20, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_warning_label, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_warning_label, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_warning_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_warning_label, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_run_label_1
     ui->lcd_run_label_1 = lv_label_create(ui->lcd_run);
    lv_label_set_text(ui->lcd_run_label_1, "缺水\n");
    lv_label_set_long_mode(ui->lcd_run_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_run_label_1, -99, 52);
    lv_obj_set_size(ui->lcd_run_label_1, 71, 28);

    //Write style for lcd_run_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_run_label_1, lv_color_hex(0xff0000), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_run_label_1, &lv_font_HT_28, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_run_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_run_label_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_run_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_run_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // The custom code of lcd_run.
      lcd_run_setup_init();

    // Update current screen layout.
    lv_obj_update_layout(ui->lcd_run);

    // Init events for screen.
    events_init_lcd_run(ui);
}
