/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"
#include "user_api.h"


void setup_scr_lcd_parameter(lv_ui *ui)
{
    //Write codes lcd_parameter
    ui->lcd_parameter = lv_obj_create(NULL);
    lv_obj_set_size(ui->lcd_parameter, 960, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_parameter, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_parameter, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->lcd_parameter, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_parameter, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_parameter, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_cont_1
    ui->lcd_parameter_cont_1 = lv_obj_create(ui->lcd_parameter);
    lv_obj_set_pos(ui->lcd_parameter_cont_1, 458, 260);
    lv_obj_set_size(ui->lcd_parameter_cont_1, 420, 100);
    lv_obj_set_scrollbar_mode(ui->lcd_parameter_cont_1, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_flag(ui->lcd_parameter_cont_1, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_add_flag(ui->lcd_parameter_cont_1, LV_OBJ_FLAG_SCROLL_ONE);

    //Write style for lcd_parameter_cont_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_parameter_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_parameter_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_parameter_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_parameter_cont_1, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_parameter_cont_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_parameter_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_parameter_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_parameter_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_parameter_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_parameter_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_line_1
    ui->lcd_parameter_line_1 = lv_line_create(ui->lcd_parameter);
    static lv_point_t lcd_parameter_line_1[] = {{0, 0},{300, 0},};
    lv_line_set_points(ui->lcd_parameter_line_1, lcd_parameter_line_1, 2);
    lv_obj_set_pos(ui->lcd_parameter_line_1, 525, 124);
    lv_obj_set_size(ui->lcd_parameter_line_1, 479, 50);

    //Write style for lcd_parameter_line_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_parameter_line_1, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_parameter_line_1, lv_color_hex(0x9c9c9c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_parameter_line_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_parameter_line_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_cont_2
    ui->lcd_parameter_cont_2 = lv_obj_create(ui->lcd_parameter);
    lv_obj_set_pos(ui->lcd_parameter_cont_2, 739, 94);
    lv_obj_set_size(ui->lcd_parameter_cont_2, 60, 60);
    lv_obj_set_scrollbar_mode(ui->lcd_parameter_cont_2, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_parameter_cont_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_parameter_cont_2, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->lcd_parameter_cont_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->lcd_parameter_cont_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->lcd_parameter_cont_2, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_parameter_cont_2, 40, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_parameter_cont_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_parameter_cont_2, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_parameter_cont_2, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_parameter_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_parameter_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_parameter_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_parameter_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_parameter_cont_2, 50, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(ui->lcd_parameter_cont_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui->lcd_parameter_cont_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui->lcd_parameter_cont_2, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(ui->lcd_parameter_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(ui->lcd_parameter_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_line_2
    ui->lcd_parameter_line_2 = lv_line_create(ui->lcd_parameter);
    static lv_point_t lcd_parameter_line_2[] = {{0, 0},{300, 0},};
    lv_line_set_points(ui->lcd_parameter_line_2, lcd_parameter_line_2, 2);
    lv_obj_set_pos(ui->lcd_parameter_line_2, 525, 54);
    lv_obj_set_size(ui->lcd_parameter_line_2, 479, 45);

    //Write style for lcd_parameter_line_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_parameter_line_2, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_parameter_line_2, lv_color_hex(0x9c9c9c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_parameter_line_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_parameter_line_2, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_cont_3
    ui->lcd_parameter_cont_3 = lv_obj_create(ui->lcd_parameter);
    lv_obj_set_pos(ui->lcd_parameter_cont_3, 558, 24);
    lv_obj_set_size(ui->lcd_parameter_cont_3, 60, 60);
    lv_obj_set_scrollbar_mode(ui->lcd_parameter_cont_3, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_parameter_cont_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_parameter_cont_3, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->lcd_parameter_cont_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->lcd_parameter_cont_3, lv_color_hex(0x9c9c9c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->lcd_parameter_cont_3, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_parameter_cont_3, 40, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_parameter_cont_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_parameter_cont_3, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_parameter_cont_3, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_parameter_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_parameter_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_parameter_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_parameter_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_parameter_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_line_3
    ui->lcd_parameter_line_3 = lv_line_create(ui->lcd_parameter);
    static lv_point_t lcd_parameter_line_3[] = {{0, 0},{300, 0},};
    lv_line_set_points(ui->lcd_parameter_line_3, lcd_parameter_line_3, 2);
    lv_obj_set_pos(ui->lcd_parameter_line_3, 525, 194);
    lv_obj_set_size(ui->lcd_parameter_line_3, 479, 60);

    //Write style for lcd_parameter_line_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_parameter_line_3, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_parameter_line_3, lv_color_hex(0x9c9c9c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_parameter_line_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_parameter_line_3, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_cont_4
    ui->lcd_parameter_cont_4 = lv_obj_create(ui->lcd_parameter);
    lv_obj_set_pos(ui->lcd_parameter_cont_4, 614, 164);
    lv_obj_set_size(ui->lcd_parameter_cont_4, 60, 60);
    lv_obj_set_scrollbar_mode(ui->lcd_parameter_cont_4, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_parameter_cont_4, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_parameter_cont_4, 15, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->lcd_parameter_cont_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->lcd_parameter_cont_4, lv_color_hex(0x9c9c9c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->lcd_parameter_cont_4, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_parameter_cont_4, 40, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_parameter_cont_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_parameter_cont_4, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_parameter_cont_4, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_parameter_cont_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_parameter_cont_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_parameter_cont_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_parameter_cont_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_parameter_cont_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_line_4
    ui->lcd_parameter_line_4 = lv_line_create(ui->lcd_parameter);
    static lv_point_t lcd_parameter_line_4[] = {{0, 0},{30, 30},};
    lv_line_set_points(ui->lcd_parameter_line_4, lcd_parameter_line_4, 2);
    lv_obj_set_pos(ui->lcd_parameter_line_4, 412, 311);
    lv_obj_set_size(ui->lcd_parameter_line_4, 55, 46);

    //Write style for lcd_parameter_line_4, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_parameter_line_4, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_parameter_line_4, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_parameter_line_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_parameter_line_4, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_line_5
    ui->lcd_parameter_line_5 = lv_line_create(ui->lcd_parameter);
    static lv_point_t lcd_parameter_line_5[] = {{0, 30},{30, 0},};
    lv_line_set_points(ui->lcd_parameter_line_5, lcd_parameter_line_5, 2);
    lv_obj_set_pos(ui->lcd_parameter_line_5, 412, 280);
    lv_obj_set_size(ui->lcd_parameter_line_5, 52, 49);

    //Write style for lcd_parameter_line_5, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_parameter_line_5, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_parameter_line_5, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_parameter_line_5, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_parameter_line_5, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_line_6
    ui->lcd_parameter_line_6 = lv_line_create(ui->lcd_parameter);
    static lv_point_t lcd_parameter_line_6[] = {{0, 0},{30, 30},};
    lv_line_set_points(ui->lcd_parameter_line_6, lcd_parameter_line_6, 2);
    lv_obj_set_pos(ui->lcd_parameter_line_6, 897, 280);
    lv_obj_set_size(ui->lcd_parameter_line_6, 55, 46);

    //Write style for lcd_parameter_line_6, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_parameter_line_6, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_parameter_line_6, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_parameter_line_6, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_parameter_line_6, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_line_7
    ui->lcd_parameter_line_7 = lv_line_create(ui->lcd_parameter);
    static lv_point_t lcd_parameter_line_7[] = {{0, 30},{30, 0},};
    lv_line_set_points(ui->lcd_parameter_line_7, lcd_parameter_line_7, 2);
    lv_obj_set_pos(ui->lcd_parameter_line_7, 897, 311);
    lv_obj_set_size(ui->lcd_parameter_line_7, 52, 45);

    //Write style for lcd_parameter_line_7, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_parameter_line_7, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_parameter_line_7, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_parameter_line_7, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_parameter_line_7, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_parameter_list_1
    ui->lcd_parameter_list_1 = lv_list_create(ui->lcd_parameter);
    lv_obj_set_pos(ui->lcd_parameter_list_1, -360, 0);
    lv_obj_set_size(ui->lcd_parameter_list_1, 360, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_parameter_list_1, LV_SCROLLBAR_MODE_AUTO);

    //Write style state: LV_STATE_DEFAULT for &style_lcd_parameter_list_1_main_main_default
    static lv_style_t style_lcd_parameter_list_1_main_main_default;
    ui_init_style(&style_lcd_parameter_list_1_main_main_default);

    lv_style_set_pad_top(&style_lcd_parameter_list_1_main_main_default, 5);
    lv_style_set_pad_left(&style_lcd_parameter_list_1_main_main_default, 5);
    lv_style_set_pad_right(&style_lcd_parameter_list_1_main_main_default, 5);
    lv_style_set_pad_bottom(&style_lcd_parameter_list_1_main_main_default, 5);
    lv_style_set_bg_opa(&style_lcd_parameter_list_1_main_main_default, 255);
    lv_style_set_bg_color(&style_lcd_parameter_list_1_main_main_default, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_parameter_list_1_main_main_default, LV_GRAD_DIR_NONE);
    lv_style_set_border_width(&style_lcd_parameter_list_1_main_main_default, 0);
    lv_style_set_radius(&style_lcd_parameter_list_1_main_main_default, 0);
    lv_style_set_shadow_width(&style_lcd_parameter_list_1_main_main_default, 0);
    lv_obj_add_style(ui->lcd_parameter_list_1, &style_lcd_parameter_list_1_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_FOCUSED for &style_lcd_parameter_list_1_main_main_focused
    static lv_style_t style_lcd_parameter_list_1_main_main_focused;
    ui_init_style(&style_lcd_parameter_list_1_main_main_focused);

    lv_style_set_pad_top(&style_lcd_parameter_list_1_main_main_focused, 5);
    lv_style_set_pad_left(&style_lcd_parameter_list_1_main_main_focused, 5);
    lv_style_set_pad_right(&style_lcd_parameter_list_1_main_main_focused, 5);
    lv_style_set_pad_bottom(&style_lcd_parameter_list_1_main_main_focused, 5);
    lv_style_set_bg_opa(&style_lcd_parameter_list_1_main_main_focused, 255);
    lv_style_set_bg_color(&style_lcd_parameter_list_1_main_main_focused, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_parameter_list_1_main_main_focused, LV_GRAD_DIR_NONE);
    lv_style_set_border_width(&style_lcd_parameter_list_1_main_main_focused, 1);
    lv_style_set_border_opa(&style_lcd_parameter_list_1_main_main_focused, 255);
    lv_style_set_border_color(&style_lcd_parameter_list_1_main_main_focused, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_lcd_parameter_list_1_main_main_focused, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_lcd_parameter_list_1_main_main_focused, 3);
    lv_style_set_shadow_width(&style_lcd_parameter_list_1_main_main_focused, 0);
    lv_obj_add_style(ui->lcd_parameter_list_1, &style_lcd_parameter_list_1_main_main_focused, LV_PART_MAIN|LV_STATE_FOCUSED);

    //Write style state: LV_STATE_DEFAULT for &style_lcd_parameter_list_1_main_scrollbar_default
    static lv_style_t style_lcd_parameter_list_1_main_scrollbar_default;
    ui_init_style(&style_lcd_parameter_list_1_main_scrollbar_default);

    lv_style_set_radius(&style_lcd_parameter_list_1_main_scrollbar_default, 3);
    lv_style_set_bg_opa(&style_lcd_parameter_list_1_main_scrollbar_default, 255);
    lv_style_set_bg_color(&style_lcd_parameter_list_1_main_scrollbar_default, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_lcd_parameter_list_1_main_scrollbar_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->lcd_parameter_list_1, &style_lcd_parameter_list_1_main_scrollbar_default, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_lcd_parameter_list_1_extra_btns_main_default
    static lv_style_t style_lcd_parameter_list_1_extra_btns_main_default;
    ui_init_style(&style_lcd_parameter_list_1_extra_btns_main_default);

    lv_style_set_pad_top(&style_lcd_parameter_list_1_extra_btns_main_default, 5);
    lv_style_set_pad_left(&style_lcd_parameter_list_1_extra_btns_main_default, 5);
    lv_style_set_pad_right(&style_lcd_parameter_list_1_extra_btns_main_default, 5);
    lv_style_set_pad_bottom(&style_lcd_parameter_list_1_extra_btns_main_default, 5);
    lv_style_set_border_width(&style_lcd_parameter_list_1_extra_btns_main_default, 0);
    lv_style_set_text_color(&style_lcd_parameter_list_1_extra_btns_main_default, lv_color_hex(0xffffff));
    lv_style_set_text_font(&style_lcd_parameter_list_1_extra_btns_main_default, &lv_font_HT_30);
    lv_style_set_text_opa(&style_lcd_parameter_list_1_extra_btns_main_default, 255);
    lv_style_set_radius(&style_lcd_parameter_list_1_extra_btns_main_default, 3);
    lv_style_set_bg_opa(&style_lcd_parameter_list_1_extra_btns_main_default, 255);
    lv_style_set_bg_color(&style_lcd_parameter_list_1_extra_btns_main_default, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_parameter_list_1_extra_btns_main_default, LV_GRAD_DIR_NONE);

    //Write style state: LV_STATE_DEFAULT for &style_lcd_parameter_list_1_extra_texts_main_default
    static lv_style_t style_lcd_parameter_list_1_extra_texts_main_default;
    ui_init_style(&style_lcd_parameter_list_1_extra_texts_main_default);

    lv_style_set_pad_top(&style_lcd_parameter_list_1_extra_texts_main_default, 20);
    lv_style_set_pad_left(&style_lcd_parameter_list_1_extra_texts_main_default, 0);
    lv_style_set_pad_right(&style_lcd_parameter_list_1_extra_texts_main_default, 0);
    lv_style_set_pad_bottom(&style_lcd_parameter_list_1_extra_texts_main_default, 0);
    lv_style_set_border_width(&style_lcd_parameter_list_1_extra_texts_main_default, 1);
    lv_style_set_border_opa(&style_lcd_parameter_list_1_extra_texts_main_default, 255);
    lv_style_set_border_color(&style_lcd_parameter_list_1_extra_texts_main_default, lv_color_hex(0xffffff));
    lv_style_set_border_side(&style_lcd_parameter_list_1_extra_texts_main_default, LV_BORDER_SIDE_BOTTOM);
    lv_style_set_text_color(&style_lcd_parameter_list_1_extra_texts_main_default, lv_color_hex(0xffffff));
    lv_style_set_text_font(&style_lcd_parameter_list_1_extra_texts_main_default, &lv_font_FY_25);
    lv_style_set_text_opa(&style_lcd_parameter_list_1_extra_texts_main_default, 255);
    lv_style_set_radius(&style_lcd_parameter_list_1_extra_texts_main_default, 0);
    lv_style_set_transform_width(&style_lcd_parameter_list_1_extra_texts_main_default, 0);
    lv_style_set_bg_opa(&style_lcd_parameter_list_1_extra_texts_main_default, 255);
    lv_style_set_bg_color(&style_lcd_parameter_list_1_extra_texts_main_default, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_parameter_list_1_extra_texts_main_default, LV_GRAD_DIR_NONE);

    //Write codes lcd_parameter_label_1
    ui->lcd_parameter_label_1 = lv_label_create(ui->lcd_parameter);
    lv_label_set_text(ui->lcd_parameter_label_1, "Label");
    lv_label_set_long_mode(ui->lcd_parameter_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->lcd_parameter_label_1, 223, -60);
    lv_obj_set_size(ui->lcd_parameter_label_1, 100, 32);

    //Write style for lcd_parameter_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_parameter_label_1, lv_color_hex(0x000000), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_parameter_label_1, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_parameter_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->lcd_parameter_label_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_parameter_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_parameter_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of lcd_parameter.
    lcd_parameter_setup_init();


    //Update current screen layout.
    lv_obj_update_layout(ui->lcd_parameter);

    //Init events for screen.
    events_init_lcd_parameter(ui);
}
