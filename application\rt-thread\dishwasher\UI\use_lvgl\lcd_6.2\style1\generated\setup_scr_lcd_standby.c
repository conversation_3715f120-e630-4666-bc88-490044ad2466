/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"
#include "user_api.h"


void setup_scr_lcd_standby(lv_ui *ui)
{
    //Write codes lcd_standby
    ui->lcd_standby = lv_obj_create(NULL);
    lv_obj_set_size(ui->lcd_standby, 960, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_standby, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_standby, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->lcd_standby, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_standby, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_standby, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_standby_animimg_1
    ui->lcd_standby_animimg_1 = lv_animimg_create(ui->lcd_standby);
    lv_animimg_set_src(ui->lcd_standby_animimg_1, (const void **) lcd_standby_animimg_1_imgs, 12);
    lv_animimg_set_duration(ui->lcd_standby_animimg_1, 50*12);
    lv_animimg_set_repeat_count(ui->lcd_standby_animimg_1, 1);
    lv_animimg_start(ui->lcd_standby_animimg_1);
    lv_obj_set_pos(ui->lcd_standby_animimg_1, 0, 0);
    lv_obj_set_size(ui->lcd_standby_animimg_1, 528, 360);

    //Write codes lcd_standby_roller_1
    ui->lcd_standby_roller_1 = lv_roller_create(ui->lcd_standby);
    lv_roller_set_options(ui->lcd_standby_roller_1, "运行\n参数设置\n硬件匹配\n系统调试", LV_ROLLER_MODE_INFINITE);
    lv_obj_set_pos(ui->lcd_standby_roller_1, 808, 89);
    lv_obj_set_width(ui->lcd_standby_roller_1, 302);

    //Write style for lcd_standby_roller_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_radius(ui->lcd_standby_roller_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_standby_roller_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_standby_roller_1, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_standby_roller_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_standby_roller_1, lv_color_hex(0xa1a1a1), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_standby_roller_1, &lv_font_HT_40, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_standby_roller_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->lcd_standby_roller_1, LV_TEXT_ALIGN_RIGHT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->lcd_standby_roller_1, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->lcd_standby_roller_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->lcd_standby_roller_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->lcd_standby_roller_1, LV_BORDER_SIDE_BOTTOM | LV_BORDER_SIDE_RIGHT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_standby_roller_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_standby_roller_1, 25, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_standby_roller_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for lcd_standby_roller_1, Part: LV_PART_SELECTED, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->lcd_standby_roller_1, 255, LV_PART_SELECTED|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_standby_roller_1, lv_color_hex(0xc8d257), LV_PART_SELECTED|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_standby_roller_1, LV_GRAD_DIR_NONE, LV_PART_SELECTED|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->lcd_standby_roller_1, lv_color_hex(0xffffff), LV_PART_SELECTED|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->lcd_standby_roller_1, &lv_font_HT_56, LV_PART_SELECTED|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->lcd_standby_roller_1, 255, LV_PART_SELECTED|LV_STATE_DEFAULT);

    lv_roller_set_visible_row_count(ui->lcd_standby_roller_1, 3);
    //The custom code of lcd_standby.
    lcd_standby_setup_init();

    //Update current screen layout.
    lv_obj_update_layout(ui->lcd_standby);

    //Init events for screen.
    events_init_lcd_standby(ui);
}
